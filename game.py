import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Polygon, Ellipse, Wedge

plt.figure(figsize=(10, 8))
ax = plt.gca()

# 画盘子
plate = Ellipse((0.5, 0.4), width=0.8, height=0.3, 
                edgecolor='lightblue', facecolor='whitesmoke', linewidth=3)
ax.add_patch(plate)

# 画粽子函数
def draw_zongzi(x, y, size=0.1, angle=0):
    # 粽叶包裹部分
    points = np.array([
        [x-size/2*np.cos(angle), y-size/2*np.sin(angle)],
        [x+size/2*np.cos(angle)-size/3*np.sin(angle), y+size/2*np.sin(angle)+size/3*np.cos(angle)],
        [x+size/2*np.cos(angle)+size/3*np.sin(angle), y+size/2*np.sin(angle)-size/3*np.cos(angle)],
        [x-size/2*np.cos(angle), y-size/2*np.sin(angle)]
    ])
    zongzi = Polygon(points, closed=True, 
                    facecolor='darkgreen', edgecolor='olive', linewidth=2)
    ax.add_patch(zongzi)
    
    # 捆绳 - 水平
    ellipse = Ellipse((x, y), width=size*0.7, height=size*0.15,
                     angle=np.degrees(angle), facecolor='gold')
    ax.add_patch(ellipse)
    
    # 捆绳 - 垂直
    ellipse = Ellipse((x, y), width=size*0.15, height=size*0.7,
                     angle=np.degrees(angle), facecolor='gold')
    ax.add_patch(ellipse)

# 绘制多个粽子
draw_zongzi(0.4, 0.35, size=0.12)
draw_zongzi(0.6, 0.35, size=0.15)
draw_zongzi(0.5, 0.45, size=0.13)
draw_zongzi(0.3, 0.4, size=1.)
draw_zongzi(.7,.4,.1,np.pi/.5)

# 添加一些装饰性的叶子
leaf_points1 = np.array([[0.0, .7], [0.1, .8], [0.2, .6]])
leaf1 = Polygon(np.array([[0.7, 0.6], [0.8, 0.7], [0.9, 0.5]]),
               closed=True, fill=False)

ax.set_xlim(), ax.set_ylim()
ax.set_aspect('equal')
plt.show()
