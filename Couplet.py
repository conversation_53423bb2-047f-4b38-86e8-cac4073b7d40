import tkinter as tk
from tkinter import font, messagebox
import random

class CoupletGenerator:
    def __init__(self):
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title("新春对联生成器")
        self.root.geometry("900x600")
        self.root.configure(bg="#f0f0f0")
        
        # 创建自定义字体
        self.title_font = font.Font(family="楷体", size=24, weight="bold")
        self.couplet_font = font.Font(family="楷体", size=32, weight="bold")
        self.button_font = font.Font(family="微软雅黑", size=14)
        
        # 初始化对联数据库
        self.couplets = [
            {"top": "天增岁月人增寿", "bottom": "春满乾坤福满门", "horizontal": "四季平安"},
            {"top": "门迎春夏秋冬福", "bottom": "户纳东西南北财", "horizontal": "财源广进"},
            {"top": "一帆风顺年年好", "bottom": "万事如意步步高", "horizontal": "吉星高照"},
            {"top": "和顺一门有百福", "bottom": "平安二字值千金", "horizontal": "万象更新"},
            {"top": "一年四季春常在", "bottom": "万紫千红永开花", "horizontal": "喜迎新春"},
            {"top": "百世岁月当代好", "bottom": "千古江山今朝新", "horizontal": "万象更新"},
            {"top": "喜居宝地千年旺", "bottom": "福照家门万事兴", "horizontal": "喜迎新春"},
            {"top": "一帆风顺吉星到", "bottom": "万事如意福临门", "horizontal": "财源广进"},
            {"top": "百年天地回元气", "bottom": "一统山河际太平", "horizontal": "国泰民安"},
            {"top": "春雨丝丝润万物", "bottom": "红梅点点绣千山", "horizontal": "春意盎然"}
        ]
        
        # 创建界面元素
        self.create_widgets()
        
        # 初始生成一个对联
        self.generate_couplet()
        
        # 运行主循环
        self.root.mainloop()
    
    def create_widgets(self):
        # 标题
        title_label = tk.Label(
            self.root, 
            text="新春对联生成器", 
            font=self.title_font, 
            fg="#d32f2f", 
            bg="#f0f0f0"
        )
        title_label.pack(pady=20)
        
        # 对联展示区域
        self.display_frame = tk.Frame(self.root, bg="#f0f0f0")
        self.display_frame.pack(pady=10)
        
        # 左侧装饰
        self.left_decoration = tk.Label(
            self.display_frame, 
            text="⺮", 
            font=("楷体", 48), 
            fg="#c62828", 
            bg="#f0f0f0"
        )
        self.left_decoration.pack(side=tk.LEFT, padx=10)
        
        # 对联内容
        self.couplet_frame = tk.Frame(self.display_frame, bg="#f0f0f0")
        self.couplet_frame.pack(side=tk.LEFT)
        
        # 横批
        self.horizontal_label = tk.Label(
            self.couplet_frame, 
            text="", 
            font=self.couplet_font, 
            fg="#d32f2f", 
            bg="#ffebee",
            width=10,
            height=1,
            relief=tk.RAISED,
            bd=3
        )
        self.horizontal_label.pack(pady=10)
        
        # 上下联容器
        self.couplet_content = tk.Frame(self.couplet_frame, bg="#f0f0f0")
        self.couplet_content.pack()
        
        # 上联
        self.top_couplet = tk.Label(
            self.couplet_content, 
            text="", 
            font=self.couplet_font, 
            fg="#000", 
            bg="#ffebee",
            width=7,
            height=7,
            relief=tk.RAISED,
            bd=3
        )
        self.top_couplet.pack(side=tk.LEFT, padx=20)
        
        # 下联
        self.bottom_couplet = tk.Label(
            self.couplet_content, 
            text="", 
            font=self.couplet_font, 
            fg="#000", 
            bg="#ffebee",
            width=7,
            height=7,
            relief=tk.RAISED,
            bd=3
        )
        self.bottom_couplet.pack(side=tk.LEFT, padx=20)
        
        # 右侧装饰
        self.right_decoration = tk.Label(
            self.display_frame, 
            text="⺮", 
            font=("楷体", 48), 
            fg="#c62828", 
            bg="#f0f0f0"
        )
        self.right_decoration.pack(side=tk.LEFT, padx=10)
        
        # 控制区域
        control_frame = tk.Frame(self.root, bg="#f0f0f0")
        control_frame.pack(pady=20)
        
        # 生成按钮
        generate_btn = tk.Button(
            control_frame,
            text="随机生成对联",
            font=self.button_font,
            bg="#d32f2f",
            fg="white",
            width=15,
            height=2,
            command=self.generate_couplet
        )
        generate_btn.pack(side=tk.LEFT, padx=20)
        
        # 自定义按钮
        custom_btn = tk.Button(
            control_frame,
            text="自定义对联",
            font=self.button_font,
            bg="#1976d2",
            fg="white",
            width=15,
            height=2,
            command=self.custom_couplet
        )
        custom_btn.pack(side=tk.LEFT, padx=20)
        
        # 保存按钮
        save_btn = tk.Button(
            control_frame,
            text="保存对联",
            font=self.button_font,
            bg="#388e3c",
            fg="white",
            width=15,
            height=2,
            command=self.save_couplet
        )
        save_btn.pack(side=tk.LEFT, padx=20)
        
        # 装饰元素
        decoration_frame = tk.Frame(self.root, bg="#f0f0f0")
        decoration_frame.pack(pady=20)
        
        tk.Label(
            decoration_frame, 
            text="❀ ❀ ❀ ❀ ❀ ❀ ❀ ❀ ❀ ❀ ❀ ❀ ❀ ❀ ❀ ❀ ❀ ❀ ❀ ❀", 
            font=("楷体", 18), 
            fg="#d32f2f", 
            bg="#f0f0f0"
        ).pack()
        
        # 状态栏
        self.status = tk.Label(
            self.root, 
            text="就绪", 
            font=("微软雅黑", 10), 
            fg="#666", 
            bg="#f0f0f0",
            bd=1,
            relief=tk.SUNKEN,
            anchor=tk.W
        )
        self.status.pack(side=tk.BOTTOM, fill=tk.X)
    
    def generate_couplet(self):
        """随机生成一副对联"""
        couplet = random.choice(self.couplets)
        self.horizontal_label.config(text=couplet["horizontal"])
        self.top_couplet.config(text=couplet["top"])
        self.bottom_couplet.config(text=couplet["bottom"])
        self.status.config(text=f"已生成新的对联: {couplet['horizontal']}")
    
    def custom_couplet(self):
        """打开自定义对联窗口"""
        self.custom_window = tk.Toplevel(self.root)
        self.custom_window.title("自定义对联")
        self.custom_window.geometry("500x400")
        self.custom_window.configure(bg="#f0f0f0")
        self.custom_window.resizable(False, False)
        
        # 标题
        tk.Label(
            self.custom_window, 
            text="自定义对联内容", 
            font=self.title_font, 
            fg="#d32f2f", 
            bg="#f0f0f0"
        ).pack(pady=20)
        
        # 表单容器
        form_frame = tk.Frame(self.custom_window, bg="#f0f0f0")
        form_frame.pack(pady=10, padx=20, fill=tk.BOTH, expand=True)
        
        # 横批输入
        tk.Label(
            form_frame, 
            text="横批:", 
            font=self.button_font, 
            bg="#f0f0f0"
        ).grid(row=0, column=0, padx=5, pady=10, sticky="w")
        
        self.horizontal_entry = tk.Entry(
            form_frame, 
            font=self.button_font, 
            width=20
        )
        self.horizontal_entry.grid(row=0, column=1, padx=5, pady=10, sticky="ew")
        self.horizontal_entry.insert(0, self.horizontal_label.cget("text"))
        
        # 上联输入
        tk.Label(
            form_frame, 
            text="上联:", 
            font=self.button_font, 
            bg="#f0f0f0"
        ).grid(row=1, column=0, padx=5, pady=10, sticky="w")
        
        self.top_entry = tk.Entry(
            form_frame, 
            font=self.button_font, 
            width=30
        )
        self.top_entry.grid(row=1, column=1, padx=5, pady=10, sticky="ew")
        self.top_entry.insert(0, self.top_couplet.cget("text"))
        
        # 下联输入
        tk.Label(
            form_frame, 
            text="下联:", 
            font=self.button_font, 
            bg="#f0f0f0"
        ).grid(row=2, column=0, padx=5, pady=10, sticky="w")
        
        self.bottom_entry = tk.Entry(
            form_frame, 
            font=self.button_font, 
            width=30
        )
        self.bottom_entry.grid(row=2, column=1, padx=5, pady=10, sticky="ew")
        self.bottom_entry.insert(0, self.bottom_couplet.cget("text"))
        
        # 按钮区域
        btn_frame = tk.Frame(self.custom_window, bg="#f0f0f0")
        btn_frame.pack(pady=20)
        
        tk.Button(
            btn_frame,
            text="应用",
            font=self.button_font,
            bg="#388e3c",
            fg="white",
            width=10,
            command=self.apply_custom
        ).pack(side=tk.LEFT, padx=10)
        
        tk.Button(
            btn_frame,
            text="取消",
            font=self.button_font,
            bg="#d32f2f",
            fg="white",
            width=10,
            command=self.custom_window.destroy
        ).pack(side=tk.LEFT, padx=10)
    
    def apply_custom(self):
        """应用自定义对联内容"""
        horizontal = self.horizontal_entry.get().strip()
        top = self.top_entry.get().strip()
        bottom = self.bottom_entry.get().strip()
        
        if not horizontal or not top or not bottom:
            messagebox.showerror("错误", "所有字段都必须填写！")
            return
        
        self.horizontal_label.config(text=horizontal)
        self.top_couplet.config(text=top)
        self.bottom_couplet.config(text=bottom)
        self.status.config(text="已应用自定义对联")
        self.custom_window.destroy()
    
    def save_couplet(self):
        """保存对联（模拟功能）"""
        messagebox.showinfo("保存成功", "对联已保存！\n\n横批: {}\n上联: {}\n下联: {}".format(
            self.horizontal_label.cget("text"),
            self.top_couplet.cget("text"),
            self.bottom_couplet.cget("text")
        ))
        self.status.config(text="对联已保存")

if __name__ == "__main__":
    app = CoupletGenerator()