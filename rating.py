import pandas as pd
from pyecharts import charts, options as opts
from pyecharts.charts import Pie
from pyecharts.globals import ThemeType

# 加载数据（示例路径，根据实际情况修改）
df = pd.read_csv("demo.xlsx")  # 或 pd.read_excel("data.xlsx")
print(df)
# 评分星级
# rates = []
# for s in df.iloc[:, 3]:
#     rates.append(s)
# sx = ["五星", "四星", "三星", "二星", "一星"]
# sy = [
#     str(rates.count(5.0) + rates.count(4.5)),
#     str(rates.count(4.0) + rates.count(3.5)),
#     str(rates.count(3.0) + rates.count(2.5)),
#     str(rates.count(2.0) + rates.count(1.5)),
#     str(rates.count(1.0) + rates.count(0.5))
# ]
# (
#     Pie(init_opts=opts.InitOpts(theme=ThemeType.CHALK, width='700px', height='400px'))
#     .add("", list(zip(sx, sy)), radius=["40%", "70%"])
#     .set_global_opts(title_opts=opts.TitleOpts(title="评分星级比例", subtitle="数据来源：猫眼电影", pos_left = "left"))
#     .set_series_opts(label_opts=opts.LabelOpts(formatter="{b}:{d}%", font_size=12))
# ).render_notebook()
