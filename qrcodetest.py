
import qrcode
from qrcode.image.pil import PilImage

# 二维码内容（链接地址或文字）
data = 'https://www.baidu.com/'

# 生成二维码，指定版本、纠错等级和边距
qr = qrcode.QRCode(
    version=1,
    error_correction=qrcode.constants.ERROR_CORRECT_L,
    box_size=10,
    border=4,
)
qr.add_data(data)
qr.make(fit=True)

# 创建二维码图像
img = qr.make_image(fill_color="black", back_color="white")

# 显示二维码（若在桌面环境）
try:
    img.show()
except Exception as e:
    print("无法显示图像:", e)

# 保存二维码
img.save('qr.png')