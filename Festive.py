import colorama
import time
import random
colorama.init(convert=True)  
RED = colorama.Fore.RED + colorama.Style.BRIGHT  
CYAN = colorama.Fore.CYAN + colorama.Style.BRIGHT  
GREEN = colorama.Fore.GREEN + colorama.Style.BRIGHT  
YELLOW = colorama.Fore.YELLOW + colorama.Style.BRIGHT  
MAGENTA = colorama.Fore.MAGENTA + colorama.Style.BRIGHT  
  
# 打印抬头  
for i in range(1, 35):  
    print('')  
# \*的位置  
heartStars = [2, 4, 8, 10, 14, 20, 26, 28, 40, 44, 52, 60, 64, 76]  
# 空格的位置  
heartBreakLines = [13, 27, 41, 55, 69, 77]  
# 玫瑰的空列位置  
flowerBreakLines = [7, 15, 23, 31, 39, 46]  
  
# 添加空列  
def addSpaces(a):  
    count = a  
    while count > 0:  
            print(' ', end='')  
            count -= 1  
  
# 添加空行  
def newLineWithSleep():  
    time.sleep(0.3)  
    print('\\n', end='')  
  
play = 0  
while play == 0:  
    Left_Spaces = random.randint(8, 80)  
    addSpaces(Left_Spaces)  
# 画心  
for i in range(0, 78):  
    if i in heartBreakLines:  
                newLineWithSleep()  
                addSpaces(Left_Spaces)  
    elif i in heartStars:  
                print(RED + '\*', end='')  
    elif i in (32, 36):  
                print(GREEN + 'M', end='')  
    elif i == 34:  
                print(GREEN + 'O', end='')  
    else:  
                print(' ', end='')  
                newLineWithSleep()  
                addSpaces(random.randint(8, 80))  
                print(CYAN + "H a p p y  M o t h e r ' s   D a y !", end='')  
                newLineWithSleep()  
                newLineWithSleep()  
                Left_Spaces = random.randint(8, 80)  
                addSpaces(Left_Spaces)  
# 画花  
for i in range(0, 47):  
    if i in flowerBreakLines:  
                newLineWithSleep()  
                addSpaces(Left_Spaces)  
    elif i in (2, 8, 12, 18):  
                print(MAGENTA + '{', end='')  
    elif i in (3, 9, 13, 19):  
                print(MAGENTA + '\_', end='')  
    elif i in (4, 10, 14, 20):  
                print(MAGENTA + '}', end='')  
    elif i in (27, 35, 43):  
                print(GREEN + '|', end='')  
    elif i in (34, 44):  
                print(GREEN + '~', end='')  
    elif i == 11:  
                print(YELLOW + 'o', end='')  
    else:  
                print(' ', end='')  
                print('\\n', end='')