import os
import comtypes
import comtypes.client
def get_file(input_path, output_path):
    # 获取所有文件名的列表
    # os.listdir(path) 获取指定路径下的所有文件名
    filename_list = os.listdir(input_path)
    # 获取所有 Word 文件名列表
    # endswith() 方法用于判断一个字符串是否以指定字符串结尾
    wordname_list = [filename for filename in filename_list
                     if filename.endswith((".doc", ".docx"))]
    for wordname in wordname_list:
        # 分离 Word 文件名称和后缀，转化为 PDF 名称
        # os.path.splitext(path) 分割路径，返回路径名和文件扩展名的元组
        pdfname = os.path.splitext(wordname)[0] + ".pdf"
        # 如果当前 Word 文件对应的 PDF 文件存在，则不转化
        if pdfname in filename_list:
            continue
        # 拼接路径和文件名
        wordpath = os.path.join(input_path, wordname)
        pdfpath = os.path.join(output_path, pdfname)
        # 生成器 这里的yield 相当于return，但是return会终止函数，而yield不会
        yield wordpath, pdfpath

def word2pdf(input_path, output_path):
    # 使用comtypes.client.CreateObject()函数创建所需的COM对象 创建一个Word应用程序对象
    word = comtypes.client.CreateObject("Word.Application")
    word.Visible = 0
    for wordpath, pdfpath in get_file(input_path, output_path):
        newpdf = word.Documents.Open(wordpath)
        newpdf.SaveAs(pdfpath, FileFormat=17)
        newpdf.Close()
        
word2pdf(r"E:\项目\疑难杂症\AI\ai-python-tools",r"E:\项目\疑难杂症\AI\ai-python-tools")

