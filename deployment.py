class Cat:
	# 属性
    color = 'black'
    # 构造方法
    def __init__(self, name):
        self.name = name
    # 自定义方法
    def eat(self, food):
        self.food = food
        print(self.name, '正在吃'+food)
    __cid = '1'
    def __run(self, speed):
        print('__cid是'+self.__cid+'的猫', '以'+speed+'的速度奔跑')
    def run(self, speed):
        self.__run(speed)
# test = Cat('小白')
# test.eat('鱼')
# print(test.name)
#print('color-->', Cat.color)
 # 创建对象
c = Cat('Tom')
# 访问属性
# print('name-->', c.name)
# print('color-->', c.color)
# 调用方法
# c.eat('鱼')
# c.run('50迈')

# 波斯猫类
class PersianCat(Cat):
    def __init__(self, name):
        self.name = name
    def eat(self, food):
        print(self.name, '正在吃'+food)
#加菲猫类
class GarfieldCat(Cat):
    def __init__(self, name):
        self.name = name
    def run(self, speed):
        print(self.name, '正在以'+speed+'的速度奔跑')
# 单继承
class SingleCat(PersianCat):
    pass
# 多继承
class MultiCat(PersianCat, GarfieldCat):
    pass

#调用
# sc = SingleCat('波斯猫1号')
# sc.eat('鱼')

# mc = MultiCat('波斯加菲猫1号')
# mc.eat('鱼')
# mc.run('50迈')

# class SingleCat(PersianCat):
#     def eat(self, food ):
#         print(self.name, '正在吃'+food, '十分钟后', self.name+'吃饱了')
# sc = SingleCat('波斯猫1号')
# sc.eat('鱼')

#open('test.txt', mode='w',encoding='utf-8')

# wf = open('test.txt', 'w', encoding='utf-8')
# wf.write('Tom\n')
# wf.writelines(['Hello\n', 'Python'])
# # 关闭
# wf.close()

# with open('test.txt', 'r', encoding='utf-8') as rf:
#     print('readline-->', rf.readline())
#     print('read-->', rf.read(6))
#     print('readlines-->', rf.readlines())

# with open('test.txt', 'rb+') as f:
#     f.write(b'123456789')
#     # 文件对象位置
#     print(f.tell())
#     # 移动到文件的第四个字节
#     f.seek(3)
#     # 读取一个字节，文件对象向后移动一位
#     print(f.read(1))
#     print(f.tell())
#     # 移动到倒数第二个字节
#     f.seek(-2, 2)
#     print(f.tell())
#     print(f.read(1))

# with open('test.txt', 'r+') as f:
#     # 检测文件对象是否连接到终端设备
#     print(f.isatty())
#     # 截取两个字节
#     f.truncate(2)
#     print(f.read())

# import os
# print(os.getcwd())

# import os
# print(os.listdir('E:/'))

# import os
# print(os.path.exists('E:/项目/疑难杂症/AI/ai-python-tools/test.txt'))

# import os
# print(os.path.getsize('E:/项目/疑难杂症/AI/ai-python-tools/test.txt'))
# print(os.path.getsize('E:/项目/疑难杂症/AI/'))


# import os
# print(os.system('ping www.baidu.com'))

# def getNum(n):
#     try:
#         return 10 / n
#     except IOError:
#         print('Error: IOError argument.')
#     except ZeroDivisionError:
#         print('Error: ZeroDivisionError argument.')
# print(getNum(0))

# 自定义异常类 MyExc
# class MyExc(Exception):  #继承Exception类
#     def __init__(self, value):
#         self.value = value
#     def __str__(self):
#         if self.value == 0:
#             return '被除数不能为0'
# # 自定义方法
# def getNum(n):
#     try:
#         if n == 0:
#             exc = MyExc(n)
#             print(exc)
#         else:
#             print(10 / n)
#     except:
#         pass

# getNum(2)

# from enum import Enum

# class WeekDay(Enum):
#     Mon = 0
#     Tue = 1
#     Wed = 2
#     Thu = 3
#     Fri = 4


# 方式 1
# for day in WeekDay:
#     # 枚举成员
#     print(day)
#     # 枚举成员名称
#     print(day.name)
#     # 枚举成员值
#     print(day.value)

# # 方式 2
# print(list(WeekDay)) 

# for i in 'Hello':
#     print(i)

# from collections import Iterable

# print(isinstance('abc', Iterable))
# print(isinstance({1, 2, 3}, Iterable))
# print(isinstance(1024, Iterable))

# def reverse(data):
#     for i in range(len(data)-1, -1, -1):
#         yield data[i]
# for char in reverse('Hello'):
#     print(char)

# 列表
# lis = [x*x for x in range(5)]
# print(lis)

# 生成器
# gen = (x*x for x in range(5))
# for g in gen:
#     print(g)

# def x(id):
#     def y(name):
#         print ('id:', id, 'name:', name)
#     return y

# y = x('ityard')
# y('程序之间')

# 装饰函数
# def funA(fun):
#     def funB(*args, **kw):
#         print('函数 ' + fun.__name__ + ' 开始执行')
#         fun(*args, **kw)
#         print('函数 ' + fun.__name__ + ' 执行完成')
#     return funB

# @funA
# # 业务函数
# def funC(name):
#   print('Hello', name)

# funC('Jhon')


# class Test(object):
#     def __init__(self, func):
#         print('函数名是 %s ' % func.__name__)
#         self.__func = func
#     def __call__(self, *args, **kwargs):
#         self.__func()
# @Test
# def hello():
#     print('Hello ...')
    
# hello()

# 全局变量
# d = 0
# def sub(a, b):
#     # d 在这为局部变量
#     d = a - b
#     print('函数内 : ', d)

# sub(9, 1)
# print('函数外 : ', d)

# import math

# x = -1.5
# print(math.ceil(x))

# import math

# x = -1.5
# print(math.fabs(x))

# import math

# x = 3
# print(math.factorial(3))

# import random

# l = [1, 2, 3, 4, 5, 6]
# print(random.sample(l, 3))

# import random

# l = [1, 2, 3, 4, 5, 6]
# random.shuffle(l)
# print(l)

# import random

# print(random.randint(1, 10))

# import random

# print(random.random())

# import sys

# print(sys.version)
# print(sys.winver)
# print(sys.platform)
# print(sys.path)
# print(sys.maxsize)
# print(sys.maxunicode)
# print(sys.copyright)
# print(sys.modules)
# print(sys.byteorder)
# print(sys.executable)

# import sys

# # 下面两行代码等价
# sys.stdout.write('Hi' + '\n')
# print('Hi')

# import sys

# s1 = input()
# s2 = sys.stdin.readline()
# print(s1)
# print(s2)
# import re

# print(re.search(r'abc', 'abcef'))
# print(re.search(r'abc', 'aBcef'))


# import re

# print(re.match(r'abc', 'abcef'))

# import re

# print(re.fullmatch(r'abc', 'abcef'))
# print(re.fullmatch(r'abc', 'abc'))

# import re

# print(re.split(r'\W+', 'ityard, ityard, ityard.'))
# print(re.split(r'(\W+)', 'ityard, ityard, ityard.'))
# print(re.split(r'\W+', 'ityard, ityard, ityard.', 1))
# print(re.split('[a-f]+', '1A2b3', flags=re.IGNORECASE))+


# import re

# print(re.findall(r'ab', 'abefabdeab'))

# import re

# pattern = re.compile(r'bc', re.I)
# print(pattern.fullmatch('Bc'))
# print(pattern.fullmatch('aBcdef', 1, 3))

# import re

# pattern = re.compile(r'bc', re.I)
# print(pattern.split('abc, aBcd, abcde.'))

# import re

# pattern = re.compile(r'bc', re.I)
# print(pattern.findall('abcefabCdeABC'))
# print(pattern.findall('abcefabCdeABC', 0, 6))

# import re

# pattern = re.compile(r'bc', re.I)
# it = pattern.finditer('12bc34BC56', 0, 6)
# for match in it:
#     print(match)

# import re

# pattern = re.compile(r'#.*$')
# str = 'ityard # 是我的名字'
# print(pattern.sub('', str))

# import re

# match = re.match(r'(?P<year>\w+) (?P<month>\w+)','2020 01')
# print(match.expand(r'现在是 \1 年 \2 月'))

# import re

# match = re.match(r'(?P<year>\w+) (?P<month>\w+)','2020 01')
# print(match.group(0))
# print(match.group(1))
# print(match.group(2))


# import re

# match = re.match(r'(?P<year>\w+) (?P<month>\w+)','2020 01')
# print(match.groups())

# from multiprocessing import Process
# import time, os

# def target():
#     time.sleep(2)
#     print ('子进程ID：', os.getpid())

# if __name__=='__main__':
#     print ('主进程ID：', os.getpid())
#     ps = []
#     for i in range(10):
#         p = Process(target=target)
#         p.start()
#         ps.append(p)
#     for p in ps:
#         p.join()


# x = 'runoob'
# for i in range(len(x)) :
#     print(len(x))


































