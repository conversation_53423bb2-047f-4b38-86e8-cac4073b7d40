import statistics

data = [1, 2, 3, 4, 5, 6, 7, 8, 9]

# 均值（Mean）
mean = statistics.mean(data)
print("Mean:", mean)  # 输出：5.0

# 中位数（Median）
median = statistics.median(data)
print("Median:", median)  # 输出：5

# 众数（Mode）
mode_data = [1, 2, 2, 3, 3, 3, 4]
mode = statistics.mode(mode_data)
print("Mode:", mode)  # 输出：3

# 标准差（Standard Deviation）
stdev = statistics.stdev(data)
print("Standard Deviation:", stdev)  # 输出：2.738...